use tauri::{State, AppHandle, Emitter};
use tracing::{info, error, warn};
use anyhow::Result;
use chrono::Utc;

use crate::app_state::AppState;
use crate::data::models::outfit_image::{
    OutfitImageRecord, OutfitImageGenerationRequest, OutfitImageGenerationResponse,
    OutfitImageStats, ProductImage
};
use crate::data::models::outfit_photo_generation::{
    WorkflowProgress
};
use crate::data::repositories::outfit_image_repository::OutfitImageRepository;
use crate::data::repositories::model_repository::ModelRepository;
use crate::data::models::model::PhotoType;
use crate::business::services::comfyui_service::ComfyUIService;
use crate::business::services::cloud_upload_service::CloudUploadService;
use crate::business::services::error_handling_service::ErrorHandlingService;
use crate::config::AppConfig;

/// 模特个人看板统计信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ModelDashboardStats {
    pub model_id: String,
    pub model_name: String,
    pub total_photos: u32,
    pub portrait_photos: u32,
    pub work_photos: u32,
    pub outfit_stats: OutfitImageStats,
    pub recent_generations: u32,
    pub success_rate: f32,
    pub favorite_count: u32,
    pub total_generation_time_ms: u64,
    pub average_generation_time_ms: u64,
}

/// 获取模特个人看板统计信息
#[tauri::command]
pub async fn get_model_dashboard_stats(
    state: State<'_, AppState>,
    model_id: String,
) -> Result<ModelDashboardStats, String> {
    println!("📊 获取模特个人看板统计信息: {}", model_id);

    // 获取数据库连接
    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database.clone());
    let model_repo = ModelRepository::new(database.clone());

    // 获取模特基本信息
    let model = model_repo.get_by_id(&model_id)
        .map_err(|e| format!("获取模特信息失败: {}", e))?
        .ok_or_else(|| "模特不存在".to_string())?;

    // 获取穿搭图片统计
    let outfit_stats = outfit_repo.get_stats_by_model_id(&model_id)
        .map_err(|e| format!("获取穿搭图片统计失败: {}", e))?;

    // 计算照片统计
    let total_photos = model.photos.len() as u32;
    let portrait_photos = model.photos.iter()
        .filter(|photo| matches!(photo.photo_type, PhotoType::Portrait))
        .count() as u32;
    let work_photos = model.photos.iter()
        .filter(|photo| matches!(photo.photo_type, PhotoType::Commercial | PhotoType::Artistic))
        .count() as u32;

    // 计算成功率
    let success_rate = if outfit_stats.total_records > 0 {
        outfit_stats.completed_records as f32 / outfit_stats.total_records as f32
    } else {
        0.0
    };

    // 获取最近30天的生成记录（简化实现，实际应该查询数据库）
    let recent_generations = outfit_stats.total_records; // TODO: 实现真正的30天统计

    // 计算总生成时间和平均生成时间（简化实现）
    let total_generation_time_ms = outfit_stats.completed_records as u64 * 5000; // 假设平均5秒
    let average_generation_time_ms = if outfit_stats.completed_records > 0 {
        total_generation_time_ms / outfit_stats.completed_records as u64
    } else {
        0
    };

    let dashboard_stats = ModelDashboardStats {
        model_id: model_id.clone(),
        model_name: model.name.clone(),
        total_photos,
        portrait_photos,
        work_photos,
        outfit_stats: outfit_stats.clone(),
        recent_generations,
        success_rate,
        favorite_count: outfit_stats.favorite_images,
        total_generation_time_ms,
        average_generation_time_ms,
    };

    println!("✅ 模特个人看板统计信息获取成功");
    Ok(dashboard_stats)
}

/// 获取模特的穿搭图片生成记录列表
#[tauri::command]
pub async fn get_outfit_image_records(
    state: State<'_, AppState>,
    model_id: String,
) -> Result<Vec<OutfitImageRecord>, String> {
    println!("📋 获取模特穿搭图片生成记录: {}", model_id);

    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database);

    let records = outfit_repo.get_records_by_model_id(&model_id)
        .map_err(|e| format!("获取穿搭图片记录失败: {}", e))?;

    println!("✅ 获取到 {} 条穿搭图片记录", records.len());
    Ok(records)
}

/// 创建穿搭图片生成记录
#[tauri::command]
pub async fn create_outfit_image_record(
    state: State<'_, AppState>,
    request: OutfitImageGenerationRequest,
) -> Result<String, String> {
    println!("🎨 创建穿搭图片生成记录: {:?}", request);

    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database);

    // 创建生成记录
    let mut record = OutfitImageRecord::new(
        request.model_id,
        request.model_image_id,
        request.generation_prompt,
    );

    println!("📝 开始处理商品图片，共 {} 个", request.product_image_paths.len());

    // 创建商品图片记录（使用异步文件操作）
    for (index, product_path) in request.product_image_paths.iter().enumerate() {
        println!("📷 处理商品图片 {}/{}: {}", index + 1, request.product_image_paths.len(), product_path);

        let file_name = std::path::Path::new(product_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or(&format!("product_{}", index))
            .to_string();

        // 使用异步文件操作，添加超时
        let file_size = match tokio::time::timeout(
            std::time::Duration::from_secs(5),
            tokio::fs::metadata(product_path)
        ).await {
            Ok(Ok(metadata)) => {
                println!("📊 获取文件大小成功: {} bytes", metadata.len());
                metadata.len()
            },
            Ok(Err(e)) => {
                println!("⚠️ 获取文件元数据失败: {}, 使用默认大小", e);
                0
            },
            Err(_) => {
                println!("⚠️ 获取文件元数据超时，使用默认大小");
                0
            }
        };

        let product_image = ProductImage::new(
            record.id.clone(),
            product_path.clone(),
            file_name,
            file_size,
        );

        record.product_images.push(product_image);
    }

    println!("💾 开始保存到数据库");

    // 使用事务保存所有数据
    match outfit_repo.create_record_with_products(&record) {
        Ok(_) => {
            println!("✅ 穿搭图片生成记录创建成功: {}", record.id);
            Ok(record.id)
        },
        Err(e) => {
            println!("❌ 创建穿搭图片记录失败: {}", e);
            Err(format!("创建穿搭图片记录失败: {}", e))
        }
    }
}

/// 删除穿搭图片生成记录
#[tauri::command]
pub async fn delete_outfit_image_record(
    state: State<'_, AppState>,
    record_id: String,
) -> Result<(), String> {
    println!("🗑️ 删除穿搭图片生成记录: {}", record_id);

    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database);

    outfit_repo.delete_record(&record_id)
        .map_err(|e| format!("删除穿搭图片记录失败: {}", e))?;

    println!("✅ 穿搭图片生成记录删除成功");
    Ok(())
}

/// 获取穿搭图片生成记录详情
#[tauri::command]
pub async fn get_outfit_image_record_detail(
    state: State<'_, AppState>,
    record_id: String,
) -> Result<Option<OutfitImageRecord>, String> {
    println!("🔍 获取穿搭图片生成记录详情: {}", record_id);

    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database);

    let record = outfit_repo.get_record_by_id(&record_id)
        .map_err(|e| format!("获取穿搭图片记录详情失败: {}", e))?;

    println!("✅ 穿搭图片生成记录详情获取成功");
    Ok(record)
}

/// 执行穿搭图片生成
#[tauri::command]
pub async fn execute_outfit_image_generation(
    state: State<'_, AppState>,
    app_handle: AppHandle,
    request: OutfitImageGenerationRequest,
) -> Result<OutfitImageGenerationResponse, String> {
    info!("🎨 执行穿搭图片生成: {:?}", request);
    let start_time = Utc::now();

    // 首先创建生成记录
    let record_id = create_outfit_image_record(state.clone(), request.clone()).await?;

    // 初始化错误处理服务
    let error_service = ErrorHandlingService::new();

    // 定义简单错误类型
    #[derive(Debug)]
    struct SimpleError(String);
    impl std::fmt::Display for SimpleError {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            write!(f, "{}", self.0)
        }
    }
    impl std::error::Error for SimpleError {}

    // 加载配置
    let config = AppConfig::load();

    // 检查 ComfyUI 是否启用
    if !config.is_comfyui_enabled() {
        let simple_error = SimpleError("ComfyUI 功能未启用".to_string());
        let user_error = error_service.handle_error(&simple_error, None);
        return Ok(OutfitImageGenerationResponse {
            record_id,
            generated_images: Vec::new(),
            generation_time_ms: 0,
            success: false,
            error_message: Some(user_error.message),
        });
    }

    // 创建 ComfyUI 服务
    let comfyui_service = ComfyUIService::new(config.get_comfyui_settings().clone());

    // 创建云上传服务
    let cloud_upload_service = CloudUploadService::new();

    // 检查 ComfyUI 连接
    if !comfyui_service.check_connection().await.unwrap_or(false) {
        let simple_error = SimpleError("ComfyUI 服务器连接失败".to_string());
        let user_error = error_service.handle_error(&simple_error, None);
        return Ok(OutfitImageGenerationResponse {
            record_id,
            generated_images: Vec::new(),
            generation_time_ms: 0,
            success: false,
            error_message: Some(user_error.message),
        });
    }

    // 获取模特图片URL（使用model_image_id）
    let database = state.get_database();
    let model_repo = ModelRepository::new(database.clone());

    // 首先获取模特的所有照片，然后找到指定的照片
    let model_image_local_path = match model_repo.get_photos(&request.model_id) {
        Ok(photos) => {
            // 查找指定ID的照片
            if let Some(photo) = photos.iter().find(|p| p.id == request.model_image_id) {
                photo.file_path.clone()
            } else {
                let simple_error = SimpleError(format!("模特图片不存在: {}", request.model_image_id));
                let user_error = error_service.handle_error(&simple_error, None);
                return Ok(OutfitImageGenerationResponse {
                    record_id,
                    generated_images: Vec::new(),
                    generation_time_ms: 0,
                    success: false,
                    error_message: Some(user_error.message),
                });
            }
        }
        Err(e) => {
            let simple_error = SimpleError(format!("获取模特图片失败: {}", e));
            let user_error = error_service.handle_error(&simple_error, None);
            return Ok(OutfitImageGenerationResponse {
                record_id,
                generated_images: Vec::new(),
                generation_time_ms: 0,
                success: false,
                error_message: Some(user_error.message),
            });
        }
    };

    // 上传模特图片到云端获取外网链接
    let model_image_url = if model_image_local_path.starts_with("https://") {
        // 如果已经是HTTPS URL，直接使用
        info!("📷 使用已有的云端模特图片URL: {}", model_image_local_path);
        model_image_local_path
    } else {
        // 如果是本地路径，上传到云存储
        info!("📤 本地模特图片需要上传到云存储: {}", model_image_local_path);
        match upload_image_to_cloud(&model_image_local_path, &cloud_upload_service).await {
            Ok(url) => url,
            Err(e) => {
                let simple_error = SimpleError(format!("上传模特图片失败: {}", e));
                let user_error = error_service.handle_error(&simple_error, None);
                return Ok(OutfitImageGenerationResponse {
                    record_id,
                    generated_images: Vec::new(),
                    generation_time_ms: 0,
                    success: false,
                    error_message: Some(user_error.message),
                });
            }
        }
    };

    // 处理多个商品图片，为每个商品图片创建生成任务
    let mut all_generated_images = Vec::new();
    let mut has_errors = false;
    let mut error_messages = Vec::new();

    // 获取工作流文件路径
    let workflow_path_str = match config.get_comfyui_settings().workflow_directory.as_ref() {
        Some(path) if !path.is_empty() => path.clone(),
        _ => {
            let simple_error = SimpleError("请先在设置中选择ComfyUI工作流文件".to_string());
            let user_error = error_service.handle_error(&simple_error, None);
            return Ok(OutfitImageGenerationResponse {
                record_id,
                generated_images: Vec::new(),
                generation_time_ms: 0,
                success: false,
                error_message: Some(user_error.message),
            });
        }
    };

    for (index, product_image_path) in request.product_image_paths.iter().enumerate() {
        info!("处理商品图片 {}/{}: {}", index + 1, request.product_image_paths.len(), product_image_path);

        // 上传商品图片到云端获取外网链接
        let product_image_url = if product_image_path.starts_with("https://") {
            // 如果已经是HTTPS URL，直接使用
            info!("📷 使用已有的云端商品图片URL: {}", product_image_path);
            product_image_path.clone()
        } else {
            // 如果是本地路径，上传到云存储
            info!("📤 本地商品图片需要上传到云存储: {}", product_image_path);
            match upload_image_to_cloud(product_image_path, &cloud_upload_service).await {
                Ok(url) => url,
                Err(e) => {
                    has_errors = true;
                    let error_msg = format!("商品图片 {} 上传失败: {}", index + 1, e);
                    error_messages.push(error_msg.clone());
                    error!("{}", error_msg);
                    continue;
                }
            }
        };

        // 创建进度回调
        let app_handle_clone = app_handle.clone();
        let record_id_clone = record_id.clone();
        let progress_callback = move |progress: WorkflowProgress| {
            // 发送进度事件到前端
            if let Err(e) = app_handle_clone.emit("outfit_generation_progress", &progress) {
                warn!("发送进度事件失败: {}", e);
            }
            info!("生成进度 - 记录ID: {}, 进度: {}%", record_id_clone, progress.progress_percentage);
        };

        let prompt = request.generation_prompt.clone().unwrap_or_else(|| "时尚穿搭生成".to_string());

        // 执行 ComfyUI 工作流并自动上传结果
        let remote_key_prefix = format!("outfit-images/{}", record_id);

        match comfyui_service.execute_workflow_with_upload(
            &cloud_upload_service,
            &workflow_path_str,
            &model_image_url,
            &product_image_url,
            &prompt,
            None, // 负面提示词
            Some(&remote_key_prefix),
            progress_callback,
        ).await {
            Ok(upload_results) => {
                // 处理上传结果
                let mut successful_uploads = 0;

                for upload_result in upload_results {
                    if upload_result.success {
                        if let Some(remote_url) = upload_result.remote_url {
                            all_generated_images.push(remote_url.clone());
                            successful_uploads += 1;
                            info!("图片上传成功: {}", remote_url);
                        }
                    } else {
                        warn!("图片上传失败: {}", upload_result.error_message.unwrap_or_default());
                    }
                }

                if successful_uploads == 0 {
                    has_errors = true;
                    error_messages.push(format!("商品图片 {}: 所有图片上传失败", index + 1));
                } else {
                    info!("商品图片 {} 生成成功，上传 {} 张图片", index + 1, successful_uploads);
                }
            }
            Err(e) => {
                has_errors = true;
                let error_msg = format!("商品图片 {} 生成异常: {}", index + 1, e);
                error_messages.push(error_msg.clone());
                error!("{}", error_msg);
            }
        }
    }

    let end_time = Utc::now();
    let total_duration = (end_time - start_time).num_milliseconds() as u64;

    // 构建响应
    let response = if all_generated_images.is_empty() {
        let error_message = if error_messages.is_empty() {
            "未生成任何图片".to_string()
        } else {
            error_messages.join("; ")
        };

        let simple_error = SimpleError(error_message.clone());
        let user_error = error_service.handle_error(&simple_error, None);

        OutfitImageGenerationResponse {
            record_id,
            generated_images: Vec::new(),
            generation_time_ms: total_duration,
            success: false,
            error_message: Some(user_error.message),
        }
    } else {
        let success_message = if has_errors {
            format!("部分生成成功，共生成 {} 张图片", all_generated_images.len())
        } else {
            format!("生成成功，共生成 {} 张图片", all_generated_images.len())
        };

        info!("{}", success_message);

        OutfitImageGenerationResponse {
            record_id,
            generated_images: all_generated_images,
            generation_time_ms: total_duration,
            success: true,
            error_message: if has_errors { Some(error_messages.join("; ")) } else { None },
        }
    };

    info!("✅ 穿搭图片生成完成，耗时: {}ms", total_duration);
    Ok(response)
}

/// 上传图片到云存储并返回可访问的URL
async fn upload_image_to_cloud(file_path: &str, cloud_service: &CloudUploadService) -> Result<String> {
    info!("📤 正在上传图片到云存储: {}", file_path);

    // 生成远程文件名
    let file_name = std::path::Path::new(file_path)
        .file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("image.jpg");

    let remote_key = format!("outfit-generation/images/{}", file_name);

    // 上传文件
    let upload_result = cloud_service
        .upload_file(file_path, Some(remote_key), None)
        .await?;

    if upload_result.success {
        if let Some(remote_url) = upload_result.remote_url {
            // 将S3 URL转换为可访问的CDN地址
            let accessible_url = convert_s3_to_cdn_url(&remote_url);
            info!("✅ 图片上传成功，S3 URL: {}", remote_url);
            info!("🌐 转换为CDN URL: {}", accessible_url);
            Ok(accessible_url)
        } else if let Some(urn) = upload_result.urn {
            // 将S3 URN转换为可访问的CDN地址
            let accessible_url = convert_s3_to_cdn_url(&urn);
            info!("✅ 图片上传成功，S3 URN: {}", urn);
            info!("🌐 转换为CDN URL: {}", accessible_url);
            Ok(accessible_url)
        } else {
            warn!("⚠️ 上传成功但未返回URL或URN");
            Err(anyhow!("上传成功但未返回URL"))
        }
    } else {
        let error_msg = upload_result.error_message
            .unwrap_or_else(|| "未知上传错误".to_string());
        error!("❌ 图片上传失败: {}", error_msg);
        Err(anyhow!("图片上传失败: {}", error_msg))
    }
}

/// 将S3 URL转换为CDN URL
fn convert_s3_to_cdn_url(s3_url: &str) -> String {
    // 如果已经是CDN URL，直接返回
    if s3_url.contains("bowongai-dev.modal.run") {
        return s3_url.to_string();
    }

    // 提取S3 key部分
    if let Some(key_start) = s3_url.find(".com/") {
        let key = &s3_url[key_start + 5..];
        format!("https://bowongai-dev.modal.run/cache/s3/{}", key)
    } else {
        // 如果无法解析，返回原URL
        s3_url.to_string()
    }
}
