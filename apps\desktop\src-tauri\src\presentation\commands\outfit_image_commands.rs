use tauri::{State, AppHandle, Emitter};
use tracing::{info, error, warn};
use std::sync::Arc;
use anyhow::Result;
use chrono::Utc;

use crate::app_state::AppState;
use crate::data::models::outfit_image::{
    OutfitImageRecord, OutfitImageGenerationRequest, OutfitImageGenerationResponse,
    OutfitImageStats, ProductImage
};
use crate::data::models::outfit_photo_generation::{
    OutfitPhotoGenerationRequest, WorkflowProgress
};
use crate::data::repositories::outfit_image_repository::OutfitImageRepository;
use crate::data::repositories::model_repository::ModelRepository;
use crate::data::models::model::PhotoType;
use crate::business::services::outfit_photo_generation_service::OutfitPhotoGenerationService;
use crate::business::services::cloud_upload_service::CloudUploadService;
use crate::business::services::error_handling_service::ErrorHandlingService;
use crate::config::AppConfig;

/// 模特个人看板统计信息
#[derive(Debug, <PERSON><PERSON>, serde::Serialize, serde::Deserialize)]
pub struct ModelDashboardStats {
    pub model_id: String,
    pub model_name: String,
    pub total_photos: u32,
    pub portrait_photos: u32,
    pub work_photos: u32,
    pub outfit_stats: OutfitImageStats,
    pub recent_generations: u32,
    pub success_rate: f32,
    pub favorite_count: u32,
    pub total_generation_time_ms: u64,
    pub average_generation_time_ms: u64,
}

/// 获取模特个人看板统计信息
#[tauri::command]
pub async fn get_model_dashboard_stats(
    state: State<'_, AppState>,
    model_id: String,
) -> Result<ModelDashboardStats, String> {
    println!("📊 获取模特个人看板统计信息: {}", model_id);

    // 获取数据库连接
    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database.clone());
    let model_repo = ModelRepository::new(database.clone());

    // 获取模特基本信息
    let model = model_repo.get_by_id(&model_id)
        .map_err(|e| format!("获取模特信息失败: {}", e))?
        .ok_or_else(|| "模特不存在".to_string())?;

    // 获取穿搭图片统计
    let outfit_stats = outfit_repo.get_stats_by_model_id(&model_id)
        .map_err(|e| format!("获取穿搭图片统计失败: {}", e))?;

    // 计算照片统计
    let total_photos = model.photos.len() as u32;
    let portrait_photos = model.photos.iter()
        .filter(|photo| matches!(photo.photo_type, PhotoType::Portrait))
        .count() as u32;
    let work_photos = model.photos.iter()
        .filter(|photo| matches!(photo.photo_type, PhotoType::Commercial | PhotoType::Artistic))
        .count() as u32;

    // 计算成功率
    let success_rate = if outfit_stats.total_records > 0 {
        outfit_stats.completed_records as f32 / outfit_stats.total_records as f32
    } else {
        0.0
    };

    // 获取最近30天的生成记录（简化实现，实际应该查询数据库）
    let recent_generations = outfit_stats.total_records; // TODO: 实现真正的30天统计

    // 计算总生成时间和平均生成时间（简化实现）
    let total_generation_time_ms = outfit_stats.completed_records as u64 * 5000; // 假设平均5秒
    let average_generation_time_ms = if outfit_stats.completed_records > 0 {
        total_generation_time_ms / outfit_stats.completed_records as u64
    } else {
        0
    };

    let dashboard_stats = ModelDashboardStats {
        model_id: model_id.clone(),
        model_name: model.name.clone(),
        total_photos,
        portrait_photos,
        work_photos,
        outfit_stats: outfit_stats.clone(),
        recent_generations,
        success_rate,
        favorite_count: outfit_stats.favorite_images,
        total_generation_time_ms,
        average_generation_time_ms,
    };

    println!("✅ 模特个人看板统计信息获取成功");
    Ok(dashboard_stats)
}

/// 获取模特的穿搭图片生成记录列表
#[tauri::command]
pub async fn get_outfit_image_records(
    state: State<'_, AppState>,
    model_id: String,
) -> Result<Vec<OutfitImageRecord>, String> {
    println!("📋 获取模特穿搭图片生成记录: {}", model_id);

    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database);

    let records = outfit_repo.get_records_by_model_id(&model_id)
        .map_err(|e| format!("获取穿搭图片记录失败: {}", e))?;

    println!("✅ 获取到 {} 条穿搭图片记录", records.len());
    Ok(records)
}

/// 创建穿搭图片生成记录
#[tauri::command]
pub async fn create_outfit_image_record(
    state: State<'_, AppState>,
    request: OutfitImageGenerationRequest,
) -> Result<String, String> {
    println!("🎨 创建穿搭图片生成记录: {:?}", request);

    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database);

    // 创建生成记录
    let mut record = OutfitImageRecord::new(
        request.model_id,
        request.model_image_id,
        request.generation_prompt,
    );

    println!("📝 开始处理商品图片，共 {} 个", request.product_image_paths.len());

    // 创建商品图片记录（使用异步文件操作）
    for (index, product_path) in request.product_image_paths.iter().enumerate() {
        println!("📷 处理商品图片 {}/{}: {}", index + 1, request.product_image_paths.len(), product_path);

        let file_name = std::path::Path::new(product_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or(&format!("product_{}", index))
            .to_string();

        // 使用异步文件操作，添加超时
        let file_size = match tokio::time::timeout(
            std::time::Duration::from_secs(5),
            tokio::fs::metadata(product_path)
        ).await {
            Ok(Ok(metadata)) => {
                println!("📊 获取文件大小成功: {} bytes", metadata.len());
                metadata.len()
            },
            Ok(Err(e)) => {
                println!("⚠️ 获取文件元数据失败: {}, 使用默认大小", e);
                0
            },
            Err(_) => {
                println!("⚠️ 获取文件元数据超时，使用默认大小");
                0
            }
        };

        let product_image = ProductImage::new(
            record.id.clone(),
            product_path.clone(),
            file_name,
            file_size,
        );

        record.product_images.push(product_image);
    }

    println!("💾 开始保存到数据库");

    // 使用事务保存所有数据
    match outfit_repo.create_record_with_products(&record) {
        Ok(_) => {
            println!("✅ 穿搭图片生成记录创建成功: {}", record.id);
            Ok(record.id)
        },
        Err(e) => {
            println!("❌ 创建穿搭图片记录失败: {}", e);
            Err(format!("创建穿搭图片记录失败: {}", e))
        }
    }
}

/// 删除穿搭图片生成记录
#[tauri::command]
pub async fn delete_outfit_image_record(
    state: State<'_, AppState>,
    record_id: String,
) -> Result<(), String> {
    println!("🗑️ 删除穿搭图片生成记录: {}", record_id);

    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database);

    outfit_repo.delete_record(&record_id)
        .map_err(|e| format!("删除穿搭图片记录失败: {}", e))?;

    println!("✅ 穿搭图片生成记录删除成功");
    Ok(())
}

/// 获取穿搭图片生成记录详情
#[tauri::command]
pub async fn get_outfit_image_record_detail(
    state: State<'_, AppState>,
    record_id: String,
) -> Result<Option<OutfitImageRecord>, String> {
    println!("🔍 获取穿搭图片生成记录详情: {}", record_id);

    let database = state.get_database();
    let outfit_repo = OutfitImageRepository::new(database);

    let record = outfit_repo.get_record_by_id(&record_id)
        .map_err(|e| format!("获取穿搭图片记录详情失败: {}", e))?;

    println!("✅ 穿搭图片生成记录详情获取成功");
    Ok(record)
}

/// 执行穿搭图片生成
#[tauri::command]
pub async fn execute_outfit_image_generation(
    state: State<'_, AppState>,
    app_handle: AppHandle,
    request: OutfitImageGenerationRequest,
) -> Result<OutfitImageGenerationResponse, String> {
    info!("🎨 执行穿搭图片生成: {:?}", request);
    let start_time = Utc::now();

    // 首先创建生成记录
    let record_id = create_outfit_image_record(state.clone(), request.clone()).await?;

    // 初始化错误处理服务
    let error_service = ErrorHandlingService::new();

    // 获取数据库连接
    let database = state.get_database();

    // 创建云上传服务
    let config = Arc::new(tokio::sync::Mutex::new(AppConfig::load()));
    let cloud_upload_service = Arc::new(CloudUploadService::new());

    // 创建穿搭照片生成服务
    let photo_generation_service = match OutfitPhotoGenerationService::new(
        database,
        config,
        cloud_upload_service,
    ) {
        Ok(service) => service,
        Err(e) => {
            error!("创建穿搭照片生成服务失败: {}", e);

            // 创建一个简单的错误实现
            #[derive(Debug)]
            struct SimpleError(String);
            impl std::fmt::Display for SimpleError {
                fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                    write!(f, "{}", self.0)
                }
            }
            impl std::error::Error for SimpleError {}

            let simple_error = SimpleError(format!("创建服务失败: {}", e));
            let user_error = error_service.handle_error(&simple_error, None);

            return Ok(OutfitImageGenerationResponse {
                record_id,
                generated_images: Vec::new(),
                generation_time_ms: 0,
                success: false,
                error_message: Some(user_error.message),
            });
        }
    };

    // 处理多个商品图片，为每个商品图片创建生成任务
    let mut all_generated_images = Vec::new();
    let mut total_generation_time = 0u64;
    let mut has_errors = false;
    let mut error_messages = Vec::new();

    for (index, product_image_path) in request.product_image_paths.iter().enumerate() {
        info!("处理商品图片 {}/{}: {}", index + 1, request.product_image_paths.len(), product_image_path);

        // 转换为OutfitPhotoGenerationRequest
        let photo_request = OutfitPhotoGenerationRequest {
            project_id: "default".to_string(), // 使用默认项目ID，或从配置中获取
            model_id: request.model_id.clone(),
            product_image_path: product_image_path.clone(),
            prompt: request.generation_prompt.clone().unwrap_or_else(|| "时尚穿搭生成".to_string()),
            negative_prompt: None, // 可以从配置中获取默认负面提示词
            workflow_config: None, // 使用默认工作流配置
        };

        // 创建进度回调
        let app_handle_clone = app_handle.clone();
        let record_id_clone = record_id.clone();
        let progress_callback = move |progress: WorkflowProgress| {
            // 发送进度事件到前端
            if let Err(e) = app_handle_clone.emit("outfit_generation_progress", &progress) {
                warn!("发送进度事件失败: {}", e);
            }
            info!("生成进度 - 记录ID: {}, 进度: {}%", record_id_clone, progress.progress_percentage);
        };

        // 执行生成
        match photo_generation_service.generate_outfit_photo(photo_request, progress_callback).await {
            Ok(response) => {
                if response.status == crate::data::models::outfit_photo_generation::GenerationStatus::Completed {
                    all_generated_images.extend(response.result_image_urls);
                    if let Some(gen_time) = response.generation_time_ms {
                        total_generation_time += gen_time;
                    }
                    info!("商品图片 {} 生成成功", index + 1);
                } else {
                    has_errors = true;
                    let error_msg = response.error_message.unwrap_or_else(|| "生成失败".to_string());
                    error_messages.push(format!("商品图片 {}: {}", index + 1, error_msg));
                    warn!("商品图片 {} 生成失败: {}", index + 1, error_msg);
                }
            }
            Err(e) => {
                has_errors = true;
                let error_msg = format!("商品图片 {} 生成异常: {}", index + 1, e);
                error_messages.push(error_msg.clone());
                error!("{}", error_msg);
            }
        }
    }

    let end_time = Utc::now();
    let total_duration = (end_time - start_time).num_milliseconds() as u64;

    // 构建响应
    let response = if all_generated_images.is_empty() {
        let error_message = if error_messages.is_empty() {
            "未生成任何图片".to_string()
        } else {
            error_messages.join("; ")
        };

        // 创建一个简单的错误实现
        #[derive(Debug)]
        struct SimpleError(String);
        impl std::fmt::Display for SimpleError {
            fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                write!(f, "{}", self.0)
            }
        }
        impl std::error::Error for SimpleError {}

        let simple_error = SimpleError(error_message.clone());
        let user_error = error_service.handle_error(&simple_error, None);

        OutfitImageGenerationResponse {
            record_id,
            generated_images: Vec::new(),
            generation_time_ms: total_duration,
            success: false,
            error_message: Some(user_error.message),
        }
    } else {
        let success_message = if has_errors {
            format!("部分生成成功，共生成 {} 张图片", all_generated_images.len())
        } else {
            format!("生成成功，共生成 {} 张图片", all_generated_images.len())
        };

        info!("{}", success_message);

        OutfitImageGenerationResponse {
            record_id,
            generated_images: all_generated_images,
            generation_time_ms: total_duration,
            success: true,
            error_message: if has_errors { Some(error_messages.join("; ")) } else { None },
        }
    };

    info!("✅ 穿搭图片生成完成，耗时: {}ms", total_duration);
    Ok(response)
}
